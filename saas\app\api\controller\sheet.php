<?php

namespace app\api\controller;

use app\BaseController;
use app\api\model\Sheet as SheetModel;

class Sheet extends BaseController
{
    // 字段类型映射
    private $typeMap = [
        'string' => '文本',
        'member' => '人员',
        'date' => '日期',
        'file' => '附件',
        'radio' => '单选',
        'check' => '多选',
        'dingshi' => '定时',
        'dingshidate' => '定时截止日期',
        'isdingshi' => '是否开启定时任务',
        'number' => '数字',
        'flow' => '流程',
        'flowNode' => '流程步骤'
    ];

    /**
     * 测试方法
     * @return \think\response\Json
     */
    public function test()
    {
        return $this->success(['message' => 'Sheet controller is working'], '测试成功');
    }

    /**
     * 获取表单树形结构列表
     * @return \think\response\Json
     */
    public function index()
    {
        try {
            // 获取筛选参数
            $filterName = $this->request->param('filter_name', '');
            $filterStatus = $this->request->param('filter_status', '');
            $filterDateStart = $this->request->param('filter_date_start', '');
            $filterDateEnd = $this->request->param('filter_date_end', '');

            // 实例化表单模型
            $sheetModel = new SheetModel();

            // 构建树形结构
            $treeData = $this->buildSheetTree($sheetModel, $filterName, $filterStatus, $filterDateStart, $filterDateEnd);

            // 构建返回数据
            $data = [
                'tree' => $treeData,
                'filters' => [
                    'filter_name' => $filterName,
                    'filter_status' => $filterStatus,
                    'filter_date_start' => $filterDateStart,
                    'filter_date_end' => $filterDateEnd,
                ],
                'total_count' => $this->countTreeNodes($treeData),
            ];

            return $this->success($data, '获取表单树形结构成功');

        } catch (\Exception $e) {
            return $this->error('获取表单树形结构失败：' . $e->getMessage());
        }
    }

    /**
     * 构建表单树形结构
     * @param SheetModel $sheetModel 表单模型
     * @param string $filterName 名称筛选
     * @param string $filterStatus 状态筛选
     * @param string $filterDateStart 开始日期
     * @param string $filterDateEnd 结束日期
     * @return array
     */
    private function buildSheetTree($sheetModel, $filterName = '', $filterStatus = '', $filterDateStart = '', $filterDateEnd = '')
    {
        // 获取所有表单数据
        $allSheets = $this->getAllSheets($sheetModel, $filterName, $filterStatus, $filterDateStart, $filterDateEnd);

        // 按层级分组
        $levelGroups = [
            0 => [], // 一级表单
            1 => [], // 二级表单
            2 => [], // 三级表单
        ];

        foreach ($allSheets as $sheet) {
            $level = $this->getSheetLevel($sheet);
            $levelGroups[$level][] = $this->formatSheetData($sheet);
        }

        // 构建树形结构
        $tree = [];

        // 处理一级表单
        foreach ($levelGroups[0] as $firstLevel) {
            $firstLevel['children'] = [];

            // 查找二级表单
            foreach ($levelGroups[1] as $secondLevel) {
                if ($secondLevel['parent_id'] == $firstLevel['id']) {
                    $secondLevel['children'] = [];

                    // 查找三级表单
                    foreach ($levelGroups[2] as $thirdLevel) {
                        if ($thirdLevel['parent_id'] == $secondLevel['id']) {
                            $secondLevel['children'][] = $thirdLevel;
                        }
                    }

                    $firstLevel['children'][] = $secondLevel;
                }
            }

            $tree[] = $firstLevel;
        }

        return $tree;
    }

    /**
     * 获取所有表单数据
     * @param SheetModel $sheetModel 表单模型
     * @param string $filterName 名称筛选
     * @param string $filterStatus 状态筛选
     * @param string $filterDateStart 开始日期
     * @param string $filterDateEnd 结束日期
     * @return array
     */
    private function getAllSheets($sheetModel, $filterName = '', $filterStatus = '', $filterDateStart = '', $filterDateEnd = '')
    {
        $query = $sheetModel->where('isDelete', 0);

        // 名称筛选
        if (!empty($filterName)) {
            $query->whereLike('name', '%' . $filterName . '%');
        }

        // 状态筛选
        if (!empty($filterStatus) && $filterStatus != '*') {
            $query->where('cate', 0);
        }

        // 日期筛选
        if (!empty($filterDateStart)) {
            $query->where('createTime', '>=', strtotime($filterDateStart));
        }

        if (!empty($filterDateEnd)) {
            $query->where('createTime', '<=', strtotime($filterDateEnd));
        }

        // 按创建时间排序
        $query->order('createTime', 'ASC');

        return $query->select()->toArray();
    }

    /**
     * 获取表单层级
     * @param array $sheet 表单数据
     * @return int
     */
    private function getSheetLevel($sheet)
    {
        if ($sheet['cate'] == 0) {
            return 0; // 一级表单
        } else {
            return $sheet['type']; // 二级表单(type=1) 或 三级表单(type=2)
        }
    }

    /**
     * 格式化表单数据
     * @param array $sheet 表单数据
     * @return array
     */
    private function formatSheetData($sheet)
    {
        return [
            'id' => $sheet['sheet_id'],
            'name' => $sheet['name'],
            'parent_id' => $sheet['cate'],
            'level' => $this->getSheetLevel($sheet),
            'type' => $sheet['type'],
            'flow_id' => $sheet['flow_id'],
            'create_time' => date('Y-m-d H:i:s', $sheet['createTime']),
            'user_id' => $sheet['userId'],
            'as_name' => $sheet['asName'],
            'is_one' => $sheet['cate'] == 0 ? '是' : '否',
            'is_two' => $sheet['type'] == 1 ? '是' : '否',
            'is_three' => $sheet['type'] == 2 ? '是' : '否',
        ];
    }

    /**
     * 统计树形结构节点总数
     * @param array $tree 树形数据
     * @return int
     */
    private function countTreeNodes($tree)
    {
        $count = 0;
        foreach ($tree as $node) {
            $count++; // 当前节点
            if (!empty($node['children'])) {
                $count += $this->countTreeNodes($node['children']); // 递归统计子节点
            }
        }
        return $count;
    }

    /**
     * 添加表单
     * @return \think\response\Json
     */
    public function add()
    {
        try {
            // 如果是GET请求，返回表单数据
            if (!$this->request->isPost()) {
                return $this->getFormData();
            }

            // 获取POST数据
            $data = $this->request->post();

            // 数据验证
            $validateResult = $this->validateForm($data);
            if ($validateResult !== true) {
                return $this->error($validateResult);
            }

            // 实例化表单模型
            $sheetModel = new SheetModel();

            // 添加表单
            $sheetId = $sheetModel->addSheet($data);

            if ($sheetId) {
                return $this->success(['sheet_id' => $sheetId], '表单添加成功');
            } else {
                return $this->error('表单添加失败');
            }

        } catch (\Exception $e) {
            return $this->error('表单添加失败：' . $e->getMessage());
        }
    }

    /**
     * 编辑表单
     * @return \think\response\Json
     */
    public function edit()
    {
        try {
            // 获取表单ID
            $sheetId = (int)$this->request->param('sheet_id', 0);
            if (!$sheetId) {
                return $this->error('表单ID不能为空');
            }

            // 如果是GET请求，返回表单数据
            if (!$this->request->isPost()) {
                return $this->getFormData($sheetId);
            }

            // 获取POST数据
            $data = $this->request->post();

            // 数据验证
            $validateResult = $this->validateForm($data, $sheetId);
            if ($validateResult !== true) {
                return $this->error($validateResult);
            }

            // 实例化表单模型
            $sheetModel = new SheetModel();

            // 编辑表单
            $result = $sheetModel->editSheet($sheetId, $data);

            if ($result) {
                return $this->success(['sheet_id' => $sheetId], '表单编辑成功');
            } else {
                return $this->error('表单编辑失败');
            }

        } catch (\Exception $e) {
            return $this->error('表单编辑失败：' . $e->getMessage());
        }
    }

    /**
     * 删除表单
     * @return \think\response\Json
     */
    public function delete()
    {
        try {
            // 验证请求方法
            if (!$this->request->isPost()) {
                return $this->error('请求方法错误');
            }

            // 获取要删除的表单ID
            $selected = $this->request->param('selected', []);
            if (empty($selected)) {
                return $this->error('请选择要删除的表单');
            }

            // 实例化表单模型
            $sheetModel = new SheetModel();

            // 验证删除
            $validateResult = $sheetModel->validateDel($selected);
            if ($validateResult !== '200') {
                return $this->error('二级和三级至少要保留一项');
            }

            // 删除表单
            $successCount = 0;
            foreach ($selected as $sheetId) {
                if ($sheetModel->deleteSheet($sheetId)) {
                    $successCount++;
                }
            }

            if ($successCount > 0) {
                return $this->success(['deleted_count' => $successCount], '删除成功');
            } else {
                return $this->error('删除失败');
            }

        } catch (\Exception $e) {
            return $this->error('删除失败：' . $e->getMessage());
        }
    }

    /**
     * 获取表单详情
     * @return \think\response\Json
     */
    public function detail()
    {
        try {
            // 获取表单ID
            $sheetId = (int)$this->request->param('sheet_id', 0);
            if (!$sheetId) {
                return $this->error('表单ID不能为空');
            }

            // 实例化表单模型
            $sheetModel = new SheetModel();

            // 获取表单详情
            $sheet = $sheetModel->getSheetInfo($sheetId);
            if (!$sheet) {
                return $this->error('表单不存在');
            }

            return $this->success($sheet, '获取表单详情成功');

        } catch (\Exception $e) {
            return $this->error('获取表单详情失败：' . $e->getMessage());
        }
    }

    /**
     * 获取二级分类
     * @return \think\response\Json
     */
    public function getSecondCate()
    {
        try {
            $filterOneSheet = $this->request->param('filter_one_sheet', '');
            $filterSecondSheet = $this->request->param('filter_second_sheet', '');
            $noall = $this->request->param('noall', false);

            // 实例化表单模型
            $sheetModel = new SheetModel();

            $options = [];

            if ($noall) {
                $options[] = ['value' => '', 'text' => ''];
            } else {
                $options[] = ['value' => '*', 'text' => '全部级别'];
            }

            if ($filterOneSheet == '' || $filterOneSheet == '*') {
                // 如果没有选择一级分类，只返回全部级别
            } else {
                $results = $sheetModel->getSheetById($filterOneSheet);
                foreach ($results as $result) {
                    $options[] = [
                        'value' => $result['sheet_id'],
                        'text' => $result['name'],
                        'selected' => $filterSecondSheet == $result['sheet_id']
                    ];
                }
            }

            return $this->success($options, '获取二级分类成功');

        } catch (\Exception $e) {
            return $this->error('获取二级分类失败：' . $e->getMessage());
        }
    }

    /**
     * 获取表单数据（用于添加/编辑表单时）
     * @param int $sheetId 表单ID（编辑时）
     * @return \think\response\Json
     */
    private function getFormData($sheetId = 0)
    {
        try {
            // 实例化表单模型
            $sheetModel = new SheetModel();

            $data = [
                'text_form' => $sheetId ? '编辑表单' : '添加表单',
                'type' => '',
                'name' => '',
                'filter_one_sheet' => '',
                'filter_one_sheet_name' => '',
                'filter_second_sheet' => '',
                'filter_second_sheet_name' => '',
                'filter_flow_id' => 0,
            ];

            // 如果是编辑，获取表单信息
            if ($sheetId) {
                $sheetInfo = $sheetModel->getSheetInfo($sheetId);
                if ($sheetInfo) {
                    $data['name'] = $sheetInfo['name'];
                    $data['type'] = $sheetInfo['type'];
                    $data['filter_flow_id'] = $sheetInfo['flow_id'];

                    if ($sheetInfo['cate'] == 0) {
                        $data['filter_one_sheet'] = $sheetInfo['sheet_id'];
                        $data['filter_one_sheet_name'] = $sheetInfo['name'];
                        $data['filter_second_sheet'] = '';
                        $data['filter_second_sheet_name'] = '';
                    } else {
                        // 获取一级表单信息
                        $oneInfo = $sheetModel->getSheetInfoById($sheetInfo['cate']);
                        if ($oneInfo) {
                            $data['filter_one_sheet'] = $oneInfo['sheet_id'];
                            $data['filter_one_sheet_name'] = $oneInfo['name'];
                        }

                        if ($sheetInfo['type'] == 1) {
                            $data['filter_second_sheet'] = $sheetInfo['sheet_id'];
                            $data['filter_second_sheet_name'] = $sheetInfo['name'];
                        }
                    }
                }
            }

            // 获取流程列表
            $data['flow_list'] = $sheetModel->getFlowList();

            // 获取一级表单列表
            $data['sheetOneList'] = $sheetModel->getOneSheet();

            return $this->success($data, '获取表单数据成功');

        } catch (\Exception $e) {
            return $this->error('获取表单数据失败：' . $e->getMessage());
        }
    }

    /**
     * 验证表单数据
     * @param array $data 表单数据
     * @param int $sheetId 表单ID（编辑时）
     * @return string|true 验证结果，true表示通过，字符串表示错误信息
     */
    private function validateForm($data, $sheetId = 0)
    {
        // 表单名称验证
        if (empty($data['name'])) {
            return '名称必填!';
        }

        // 流程验证
        if (isset($data['sheet_type']) && $data['sheet_type'] == 1) {
            if (empty($data['flow_id'])) {
                return '请选择流程!';
            }
        }

        // 三级视图验证
        if (isset($data['filter_second_sheet']) && $data['filter_second_sheet'] != '') {
            return '三级视图需要在前端页面添加/编辑人员添加!';
        }

        // 实例化表单模型
        $sheetModel = new SheetModel();

        // 名称重复验证
        $validateResult = $sheetModel->validateName($data, $sheetId);
        if ($validateResult != '200') {
            return '名称重复!';
        }

        return true;
    }
}
